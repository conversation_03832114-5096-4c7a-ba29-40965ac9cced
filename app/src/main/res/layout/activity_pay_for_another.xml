<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/cl_mine2"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_result"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30dp"
                android:drawableTop="@drawable/icon_pay_for_another_submit_success"
                android:drawablePadding="10dp"
                android:text="@string/str_pay_for_another_result_success"
                android:textColor="@color/text_292933"
                android:textSize="17dp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="7dp"
                android:gravity="center"
                android:text="@string/str_pay_for_another_result_tips"
                android:textColor="@color/text_9494A6"
                android:textSize="12dp" />

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/rtv_pay_for_another"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginStart="20dp"
                android:layout_marginTop="40dp"
                android:layout_marginEnd="20dp"
                android:gravity="center"
                android:text="@string/str_pay_for_another_btn_pay"
                android:textColor="@color/white"
                android:textSize="16dp"
                app:rv_backgroundColor="@color/color_00B377"
                app:rv_cornerRadius="2dp" />

            <TextView
                android:id="@+id/rtv_order"
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:layout_gravity="center"
                android:layout_marginStart="20dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="20dp"
                android:gravity="center"
                android:text="@string/str_pay_for_another_btn_order"
                android:textColor="@color/text_9494A6"
                android:textSize="16dp" />
        </LinearLayout>

        <ImageView
            android:id="@+id/ll_result_img"
            android:layout_width="127dp"
            android:layout_height="@dimen/dimen_dp_16"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:layout_marginBottom="@dimen/dimen_dp_10"
            android:src="@drawable/icon_doyoulike" />
        <!-- 推荐商品流 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dimen_dp_36"
            android:orientation="vertical"

            >

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/crv_pfa_recommend"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/dimen_dp_5"
                android:layout_marginEnd="@dimen/dimen_dp_5" />
        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</LinearLayout>
