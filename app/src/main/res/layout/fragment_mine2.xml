<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/more_ll"
    android:background="@color/color_F7F7F7"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/cl_mine2"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/abl_mine2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fadingEdge="none"
            app:elevation="@dimen/dimen_dp_0">

            <include
                layout="@layout/header_mine2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_anchorGravity="center"
                app:layout_scrollFlags="scroll" />
        </com.google.android.material.appbar.AppBarLayout>

        <!-- 推荐商品流 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            app:layout_scrollFlags="scroll|enterAlways">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/crv_recommend"
                android:layout_marginStart="@dimen/dimen_dp_5"
                android:layout_marginEnd="@dimen/dimen_dp_5"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/header_height"
        android:gravity="bottom"
        android:background="@color/white"
        android:visibility="visible"
        tools:alpha="1"
        tools:visibility="visible">

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="我的"
            android:alpha="0"
            android:textColor="@color/color_292933"
            android:textSize="18dp"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/iv_mine2_message_title"
            android:layout_width="@dimen/dimen_dp_20"
            android:layout_height="@dimen/dimen_dp_20"
            android:layout_marginEnd="@dimen/dimen_dp_11"
            android:src="@drawable/icon_mine2_message"
            android:alpha="0"
            app:layout_constraintBottom_toBottomOf="@+id/title_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/title_tv" />

        <TextView
            android:id="@+id/tv_mine2_message_bubble_title"
            android:layout_width="@dimen/dimen_dp_15"
            android:layout_height="@dimen/dimen_dp_15"
            android:background="@drawable/shape_home_steady_message_bubble"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_11"
            android:gravity="center"
            android:visibility="visible"
            android:alpha="0"
            app:layout_constraintCircle="@id/iv_mine2_message_title"
            app:layout_constraintCircleAngle="45"
            app:layout_constraintCircleRadius="@dimen/dimen_dp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="9+" />

        <ImageView
            android:id="@+id/iv_mine2_setting_title"
            android:layout_width="@dimen/dimen_dp_22"
            android:layout_height="@dimen/dimen_dp_22"
            android:layout_marginEnd="@dimen/dimen_dp_21"
            android:alpha="0"
            android:src="@drawable/icon_mine2_setting"
            app:layout_constraintBottom_toBottomOf="@+id/title_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iv_mine2_message_title"
            app:layout_constraintTop_toTopOf="@+id/title_tv" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <RelativeLayout
        android:id="@+id/rl_mine_gift_pop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_vip_gift"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="46dp"
            android:src="@drawable/icon_vip_gift" />

        <ImageView
            android:id="@+id/iv_vip_gift_close"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"
            android:src="@drawable/icon_vip_gift_close" />

    </RelativeLayout>
</RelativeLayout>